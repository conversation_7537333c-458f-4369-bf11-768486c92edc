"use client";

import { ContentItem, toggleFavourite } from "@/features/content/contentSlice";
import { useAppDispatch } from "@/redux/hook";
import clsx from "clsx";
import { Heart, Play } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function newsSection({ item }: { item: ContentItem }) {
  const dispatch = useAppDispatch();

  const handleFavourite = () => {
    dispatch(toggleFavourite(item.id));
  };

  return (
    <div className="flex flex-col rounded-xl shadow-md bg-white dark:bg-gray-800 overflow-hidden hover:shadow-lg transition-shadow h-full">
      {/* Image */}
      <div className="relative w-full aspect-video overflow-hidden">
        <Image
          src={item.image || "/placeholder.png"}
          alt={item.title}
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          className="object-cover rounded-t-xl"
          priority={false}
        />
      </div>

      {/* Card Body */}
      <div className="flex flex-col flex-1 p-4 gap-3">
        {/* Title & Favourite Button */}
        <div className="flex justify-between items-start">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white line-clamp-2">
            {item.title}
          </h3>
          <button
            onClick={handleFavourite}
            className={clsx(
              "p-1 rounded-full transition-colors",
              item.isFavourite
                ? "text-red-500"
                : "text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            )}
          >
            <Heart fill={item.isFavourite ? "currentColor" : "none"} />
          </button>
        </div>

        {/* Description */}
        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">
          {item.description}
        </p>

        {/* CTA */}
        <div className="mt-auto pt-2">
          <Link
            href={item.url}
            target="_blank"
            className="inline-flex items-center text-blue-600 dark:text-blue-400 font-medium hover:underline"
          >
            {item.type === "spotify" ? (
              <>
                <Play size={16} className="mr-1" />
                Play Now
              </>
            ) : (
              <>Read More</>
            )}
          </Link>
        </div>
      </div>
    </div>
  );
}
