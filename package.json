{"name": "pgagi-assignment", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "axios": "^1.10.0", "clsx": "^2.1.1", "debounce": "^2.2.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.524.0", "next": "15.3.4", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}