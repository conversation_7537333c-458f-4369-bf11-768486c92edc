import SessionWrapper from "@/components/SessionWrapper";
import { Providers } from "@/redux/provider";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "PGAGI Assignment",
  description: "Frontend dashboard project",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <SessionWrapper>
          <Providers>{children}</Providers>
        </SessionWrapper>
      </body>
    </html>
  );
}
